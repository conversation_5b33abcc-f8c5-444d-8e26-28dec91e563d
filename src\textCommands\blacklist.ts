/**
 * Blacklist Command
 * Allows bot developers to blacklist problematic servers
 */
import { EmbedBuilder } from 'discord.js';
import { Message, Client } from 'discord.js';
import { isDeveloper } from '../constants/devs';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';
import { EMOJI } from '../constants/emoji';

// Define an interface for the blacklist entry structure
interface BlacklistEntry {
  reason: string;
  blacklistedBy: string;
  blacklistedAt: string; // ISO Date String
}

// Define an interface for the blacklist file structure
interface BlacklistData {
  blacklistedServers: { [key: string]: BlacklistEntry };
}

export const name = 'blacklist';
export const description = 'Blacklist a server from using the bot';
export const usage = 'blacklist <add/remove/list> [server_id] [reason]';
export const devOnly = true; // Only developers can use this command
export const adminOnly = false;

export const execute = async (message: Message, args: string[], client: Client) => {
  // 1. Enforce developer-only access
  if (!isDeveloper(message.author.id)) {
    return message.reply({
      content: 'You do not have permission to use this command.',
      allowedMentions: { repliedUser: false },
    });
  }

  try {
    // Check if subcommand was provided
    if (!args[0]) {
      return message.reply({
        content: 'Please specify a subcommand: `add`, `remove`, or `list`',
        allowedMentions: { repliedUser: false },
      });
    }

    const subCommand = args[0].toLowerCase();

    // Load blacklisted servers
    const blacklist: BlacklistData = await dataManager.loadBlacklistedServers();

    switch (subCommand) {
      case 'add': {
        // Check if server ID was provided
        if (!args[1]) {
          return message.reply({
            content: 'Please specify a server ID to blacklist',
            allowedMentions: { repliedUser: false },
          });
        }

        const serverId = args[1];
        // Validate server ID (Discord snowflake: 17-20 digit string)
        if (!/^\d{17,20}$/.test(serverId)) {
          return message.reply({
            content: 'Invalid server ID. Please provide a valid Discord server ID.',
            allowedMentions: { repliedUser: false },
          });
        }
        // Get reason (everything after server ID)
        let reason = args.slice(2).join(' ') || 'No reason provided';
        if (reason.length > 256) reason = reason.slice(0, 256);
        // Check if server is already blacklisted
        if (blacklist.blacklistedServers[serverId]) {
          const entry = blacklist.blacklistedServers[serverId];
          return message.reply({
            content: `Server with ID \`${serverId}\` is already blacklisted.\n• Reason: ${entry.reason}\n• By: <@${entry.blacklistedBy}>\n• When: <t:${Math.floor(new Date(entry.blacklistedAt).getTime()/1000)}:R>`,
            allowedMentions: { repliedUser: false },
          });
        }
        // Add server to blacklist
        blacklist.blacklistedServers[serverId] = {
          reason: reason,
          blacklistedBy: message.author.id,
          blacklistedAt: new Date().toISOString(),
        };

        // Save updated blacklist
        const saved = await dataManager.saveBlacklistedServers(blacklist);

        if (saved) {
          logger.info(`[BLACKLIST] ${message.author.tag} (${message.author.id}) blacklisted server ${serverId} for reason: ${reason}`);
          // Try to leave the server if we're in it
          const guild = client.guilds.cache.get(serverId);

          if (guild) {
            try {
              await guild.leave();

              // Send success message with leave confirmation
              await message.reply({
                content: `${EMOJI[client.user.id].CHECK} Server \`${serverId}\` has been blacklisted and the bot has left the server.\nReason: ${reason}`,
                allowedMentions: { repliedUser: false },
              });
            } catch (error) {
              logger.error('Failed to leave blacklisted server:', error);

              // Send success message without leave confirmation
              await message.reply({
                content: `${EMOJI[client.user.id].CHECK} Server \`${serverId}\` has been blacklisted, but the bot failed to leave the server.\nReason: ${reason}`,
                allowedMentions: { repliedUser: false },
              });
            }
          } else {
            // Send success message
            await message.reply({
              content: `${EMOJI[client.user.id].CHECK} Server \`${serverId}\` has been blacklisted.\nReason: ${reason}`,
              allowedMentions: { repliedUser: false },
            });
          }
        } else {
          throw new Error('Failed to save blacklist');
        }

        break;
      }

      case 'remove': {
        // Check if server ID was provided
        if (!args[1]) {
          return message.reply({
            content: 'Please specify a server ID to remove from the blacklist',
            allowedMentions: { repliedUser: false },
          });
        }

        const serverId = args[1];

        // Check if server is blacklisted
        if (!blacklist.blacklistedServers[serverId]) {
          return message.reply({
            content: `Server with ID \`${serverId}\` is not blacklisted.`,
            allowedMentions: { repliedUser: false },
          });
        }

        // Store reason for logs
        const reason = blacklist.blacklistedServers[serverId].reason;

        // Remove server from blacklist
        delete blacklist.blacklistedServers[serverId];

        // Save updated blacklist
        const saved = await dataManager.saveBlacklistedServers(blacklist);

        if (saved) {
          logger.info(`[BLACKLIST] ${message.author.tag} (${message.author.id}) removed server ${serverId} from blacklist (original reason: ${reason})`);
          // Send success message
          await message.reply({
            content: `${EMOJI[client.user.id].CHECK} Server \`${serverId}\` has been removed from the blacklist.\nOriginal reason: ${reason}`,
            allowedMentions: { repliedUser: false },
          });
        } else {
          throw new Error('Failed to save blacklist');
        }

        break;
      }

      case 'list': {
        // Check if there are any blacklisted servers
        const blacklistedServers = Object.entries(blacklist.blacklistedServers);

        if (blacklistedServers.length === 0) {
          return message.reply({
            content: 'There are no blacklisted servers.',
            allowedMentions: { repliedUser: false },
          });
        }

        // Format the list of blacklisted servers
        let formattedList = blacklistedServers
          .map(([id, data]: [string, BlacklistEntry]) => {
            // Try to get server name if the bot is still in the server
            const guild = client.guilds.cache.get(id);
            const serverName = guild ? `${guild.name} (${id})` : id;
            return `• **${serverName}**\n  • Reason: ${data.reason}\n  • By: <@${data.blacklistedBy}>\n  • When: <t:${Math.floor(new Date(data.blacklistedAt).getTime()/1000)}:R>`;
          });
        let tooMany = false;
        if (formattedList.length > 10) {
          formattedList = formattedList.slice(0, 10);
          tooMany = true;
        }
        const embed = new EmbedBuilder()
          .setColor(0xff0000)
          .setTitle('Blacklisted Servers')
          .setDescription(formattedList.join('\n\n') + (tooMany ? `\n\n...and ${blacklistedServers.length - 10} more. Use \\list to view all.` : ''))
          .setFooter({
            text: `Requested by ${message.author.tag}`,
            iconURL: message.author.displayAvatarURL(),
          })
          .setTimestamp();
        await message.reply({ embeds: [embed], allowedMentions: { repliedUser: false } });
        break;
      }

      default:
        return message.reply({
          content: 'Invalid subcommand. Available subcommands: `add`, `remove`, `list`',
          allowedMentions: { repliedUser: false },
        });
    }
  } catch (error) {
    logger.error('Error in blacklist command:', error);
    message
      .reply({
        content: `An error occurred: ${error.message}`,
        allowedMentions: { repliedUser: false },
      })
      .catch(err => logger.error('Error sending error message:', err));
  }
};
