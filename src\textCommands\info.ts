/**
 * Info Command
 * Displays information about the bot in Carl-bot style
 */
import { EmbedBuilder } from 'discord.js';
import { Message } from 'discord.js';
import { formatUptime, formatBytes } from '../utils/helpers';
import { DEVELOPER_IDS } from '../constants/devs';
import logger from '../utils/logger';
import { ExtendedClient } from '../types';

export const name = 'info';
export const description = 'Displays information about the bot';
export const devOnly = false;
export const adminOnly = false;

/**
 * Fetch developer info for the embed
 * @param {ExtendedClient} client - Discord.js client
 * @returns {string} - Formatted developer info
 */
async function fetchDeveloperInfo(client: ExtendedClient): Promise<string> {
  try {
    return DEVELOPER_IDS.map(id => `<@${id}>`).join(', ');
  } catch (error) {
    logger.error('Error fetching developer info:', error);
    return DEVELOPER_IDS.map(id => `<@${id}>`).join(', ');
  }
} 

export const execute = async (message: Message, _args: string[], client: ExtendedClient) => {
  try {
    // Get memory usage
    const memoryUsage = process.memoryUsage();
    const totalMemory = memoryUsage.rss;

    // Calculate CPU usage (simplified)
    const cpuUsage = process.cpuUsage();
    const cpuPercent =
      ((cpuUsage.user + cpuUsage.system) / 1000000 / (process.uptime() * 1000)) * 100;

    // Get total members across all guilds
    const totalMembers = client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0);

    // Get total channels across all guilds
    const totalChannels = client.guilds.cache.reduce(
      (acc, guild) => acc + guild.channels.cache.size,
      0
    );

    // Get total voice channels
    const totalVoiceChannels = client.guilds.cache.reduce(
      (acc, guild) => acc + guild.channels.cache.filter(c => c.type === 2).size,
      0
    );

    // Get bot statistics
    const stats = global.getStats
      ? global.getStats()
      : {
          totalMessagesProcessed: 0,
          totalCommandsUsed: 0,
          startTime: Date.now(),
        };

    // Calculate uptime
    const uptime = formatUptime(client.uptime);

    // Calculate messages per second
    const uptimeSeconds = (Date.now() - stats.startTime) / 1000;
    const messagesPerSecond =
      uptimeSeconds > 0 ? (stats.totalMessagesProcessed / uptimeSeconds).toFixed(1) : '0.0';

    // Get developer information
    const developerInfo = await fetchDeveloperInfo(client);

    // Create the info embed in Carl-bot style
    const infoEmbed = new EmbedBuilder()
      .setColor(0x36393f) // Dark gray like Carl-bot
      .setAuthor({
        name: `${client.user.username}`,
        iconURL: client.user.displayAvatarURL(),
      })
      .setDescription([
  '✨ **MyVC** — Effortless, intelligent voice channel management.',
  '─────────────────────────────',
  '🔗 **Links**',
  '• [Dashboard](https://example.com/dashboard) | [Invite](https://discord.com/oauth2/authorize?client_id=' + client.user.id + '&permissions=8&scope=bot) | [Commands](https://example.com/commands)',
  '• [Support](https://discord.gg/example) | [Vote](https://example.com/vote) | [Patreon](https://patreon.com/example)',
  '─────────────────────────────',
  '📝 **About**',
  '> MyVC automatically creates, manages, and cleans up voice channels for you—so your community always has the perfect space to connect. Enjoy a smoother, smarter, and spam-free Discord experience.',
  '',
  '🌍 **Global Sync**',
  '> Your user settings are saved globally—enjoy a seamless experience anywhere you use MyVC!',
  '',
  '_Join thousands of communities making the switch to MyVC!_',
].join('\n'))
      .addFields(
  {
    name: '👥 Members',
    value: `**${totalMembers.toLocaleString()}** total\n${client.guilds.cache.size.toLocaleString()} servers`,
    inline: true,
  },
  {
    name: '💾 Process',
    value: `${formatBytes(totalMemory)} RAM\n${cpuPercent.toFixed(1)}% CPU`,
    inline: true,
  },
  {
    name: '⏱️ Uptime',
    value: uptime,
    inline: true,
  },
  {
    name: '👨‍💻 Developers',
    value: developerInfo,
    inline: false,
  }
)
      .setFooter({
        text: `Made with ❤️ with Discord.js`,
        iconURL:
          'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg',
      });

    // Send the embed
    await message.reply({ embeds: [infoEmbed], allowedMentions: { repliedUser: false } });
  } catch (error) {
    logger.error('Error in info command:', error);
    message
      .reply({
        content: 'An error occurred while getting bot info.',
        allowedMentions: { repliedUser: false },
      })
      .catch(err => logger.error('Error sending error message:', err));
  }
};
